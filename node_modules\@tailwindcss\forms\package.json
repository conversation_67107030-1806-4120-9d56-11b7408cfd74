{"name": "@tailwindcss/forms", "version": "0.5.0", "main": "src/index.js", "license": "MIT", "repository": "https://github.com/tailwindlabs/tailwindcss-forms", "publishConfig": {"access": "public"}, "prettier": {"printWidth": 100, "semi": false, "singleQuote": true, "trailingComma": "es5"}, "scripts": {"dev": "concurrently \"npm run serve\" \"npm run watch\"", "serve": "live-server .", "watch": "npm run build -- -w", "build": "tailwindcss -o dist/tailwind.css"}, "peerDependencies": {"tailwindcss": ">=3.0.0 || >= 3.0.0-alpha.1"}, "devDependencies": {"autoprefixer": "^10.4.2", "concurrently": "^5.3.0", "live-server": "^1.2.1", "postcss": "^8.4.5", "tailwindcss": "^3.0.2"}, "dependencies": {"mini-svg-data-uri": "^1.2.3"}}