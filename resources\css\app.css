@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  body {
    font-family: 'Inter', sans-serif;
  }
}

@layer components {
  .btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-200;
  }

  .btn-secondary {
    @apply bg-secondary-100 hover:bg-secondary-200 text-secondary-800 font-semibold py-3 px-6 rounded-lg transition-colors duration-200;
  }

  .gradient-bg {
    @apply bg-gradient-to-br from-primary-500 via-primary-600 to-primary-700;
  }

  .card {
    @apply bg-white rounded-xl shadow-lg border border-secondary-200;
  }
}

@layer utilities {
  /* Custom animations */
  .animate-fade-in-up {
    animation: fadeInUp 1s ease-out;
  }

  .animate-fade-in-right {
    animation: fadeInRight 1s ease-out 0.3s both;
  }

  .animate-gradient-x {
    animation: gradientX 3s ease infinite;
    background-size: 200% 200%;
  }

  .animate-scale-x {
    animation: scaleX 2s ease-in-out infinite;
  }

  .animate-float {
    animation: float 3s ease-in-out infinite;
  }

  .shadow-3xl {
    box-shadow: 0 35px 60px -12px rgba(0, 0, 0, 0.25);
  }
}

/* Keyframe animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes gradientX {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

@keyframes scaleX {
  0%, 100% {
    transform: scaleX(0);
  }
  50% {
    transform: scaleX(1);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* Responsive improvements */
@media (max-width: 768px) {
  .animate-fade-in-up {
    animation-delay: 0.2s;
  }

  .animate-fade-in-right {
    animation: fadeInUp 1s ease-out 0.5s both;
  }
}