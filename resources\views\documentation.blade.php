@extends('layouts.app')

@section('title', 'Documentation - LeadWave WhatsApp Desktop')
@section('description', 'Complete documentation for LeadWave WhatsApp Desktop. Learn how to set up, configure, and use all features effectively.')

@section('content')
<!-- Hero Section -->
<section class="bg-gradient-to-br from-primary-50 to-secondary-50 py-20">
    <div class="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
        <h1 class="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
            <span class="text-primary-600">Documentation</span>
        </h1>
        <p class="text-xl text-gray-600 mb-8">
            Everything you need to know to get the most out of LeadWave WhatsApp Desktop.
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="#getting-started" class="btn-primary">Getting Started</a>
            <a href="#api-docs" class="btn-secondary">API Documentation</a>
        </div>
    </div>
</section>

<!-- Quick Navigation -->
<section class="py-12 bg-white border-b">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid md:grid-cols-4 gap-6">
            <a href="#getting-started" class="flex items-center p-4 rounded-lg hover:bg-gray-50 transition-colors">
                <div class="w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center mr-3">
                    <svg class="w-5 h-5 text-primary-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.293l-3-3a1 1 0 00-1.414 1.414L10.586 9H7a1 1 0 100 2h3.586l-1.293 1.293a1 1 0 101.414 1.414l3-3a1 1 0 000-1.414z" clip-rule="evenodd"/>
                    </svg>
                </div>
                <div>
                    <div class="font-semibold text-gray-900">Getting Started</div>
                    <div class="text-sm text-gray-600">Installation & setup</div>
                </div>
            </a>
            
            <a href="#features" class="flex items-center p-4 rounded-lg hover:bg-gray-50 transition-colors">
                <div class="w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center mr-3">
                    <svg class="w-5 h-5 text-primary-600" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                </div>
                <div>
                    <div class="font-semibold text-gray-900">Features Guide</div>
                    <div class="text-sm text-gray-600">How to use features</div>
                </div>
            </a>
            
            <a href="#api-docs" class="flex items-center p-4 rounded-lg hover:bg-gray-50 transition-colors">
                <div class="w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center mr-3">
                    <svg class="w-5 h-5 text-primary-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M12.316 3.051a1 1 0 01.633 1.265l-4 12a1 1 0 11-1.898-.632l4-12a1 1 0 011.265-.633zM5.707 6.293a1 1 0 010 1.414L3.414 10l2.293 2.293a1 1 0 11-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0zm8.586 0a1 1 0 011.414 0l3 3a1 1 0 010 1.414l-3 3a1 1 0 11-1.414-1.414L16.586 10l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd"/>
                    </svg>
                </div>
                <div>
                    <div class="font-semibold text-gray-900">API Reference</div>
                    <div class="text-sm text-gray-600">Developer resources</div>
                </div>
            </a>
            
            <a href="#troubleshooting" class="flex items-center p-4 rounded-lg hover:bg-gray-50 transition-colors">
                <div class="w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center mr-3">
                    <svg class="w-5 h-5 text-primary-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"/>
                    </svg>
                </div>
                <div>
                    <div class="font-semibold text-gray-900">Troubleshooting</div>
                    <div class="text-sm text-gray-600">Common issues</div>
                </div>
            </a>
        </div>
    </div>
</section>

<!-- Documentation Content -->
<section class="py-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid lg:grid-cols-4 gap-8">
            <!-- Sidebar Navigation -->
            <div class="lg:col-span-1">
                <div class="sticky top-8">
                    <nav class="space-y-1">
                        <div class="pb-4">
                            <h3 class="text-sm font-semibold text-gray-900 uppercase tracking-wide">Getting Started</h3>
                            <ul class="mt-2 space-y-1">
                                <li><a href="#installation" class="text-gray-600 hover:text-primary-600 text-sm">Installation</a></li>
                                <li><a href="#first-setup" class="text-gray-600 hover:text-primary-600 text-sm">First Setup</a></li>
                                <li><a href="#connecting-whatsapp" class="text-gray-600 hover:text-primary-600 text-sm">Connecting WhatsApp</a></li>
                            </ul>
                        </div>
                        
                        <div class="pb-4">
                            <h3 class="text-sm font-semibold text-gray-900 uppercase tracking-wide">Features</h3>
                            <ul class="mt-2 space-y-1">
                                <li><a href="#bulk-messaging" class="text-gray-600 hover:text-primary-600 text-sm">Bulk Messaging</a></li>
                                <li><a href="#templates" class="text-gray-600 hover:text-primary-600 text-sm">Message Templates</a></li>
                                <li><a href="#ai-chatbot" class="text-gray-600 hover:text-primary-600 text-sm">AI Chatbot</a></li>
                                <li><a href="#contact-management" class="text-gray-600 hover:text-primary-600 text-sm">Contact Management</a></li>
                            </ul>
                        </div>
                        
                        <div class="pb-4">
                            <h3 class="text-sm font-semibold text-gray-900 uppercase tracking-wide">Advanced</h3>
                            <ul class="mt-2 space-y-1">
                                <li><a href="#api-integration" class="text-gray-600 hover:text-primary-600 text-sm">API Integration</a></li>
                                <li><a href="#webhooks" class="text-gray-600 hover:text-primary-600 text-sm">Webhooks</a></li>
                                <li><a href="#automation" class="text-gray-600 hover:text-primary-600 text-sm">Automation Rules</a></li>
                            </ul>
                        </div>
                    </nav>
                </div>
            </div>
            
            <!-- Main Content -->
            <div class="lg:col-span-3">
                <div class="prose prose-lg max-w-none">
                    <!-- Getting Started Section -->
                    <div id="getting-started" class="mb-16">
                        <h2 class="text-3xl font-bold text-gray-900 mb-8">Getting Started</h2>
                        
                        <div id="installation" class="mb-12">
                            <h3 class="text-2xl font-semibold text-gray-900 mb-4">Installation</h3>
                            <div class="bg-gray-50 rounded-lg p-6 mb-6">
                                <h4 class="font-semibold text-gray-900 mb-3">System Requirements</h4>
                                <ul class="space-y-2 text-gray-600">
                                    <li>• Windows 10/11, macOS 10.15+, or Linux Ubuntu 18.04+</li>
                                    <li>• 4GB RAM minimum (8GB recommended)</li>
                                    <li>• 500MB free disk space</li>
                                    <li>• Stable internet connection</li>
                                </ul>
                            </div>
                            
                            <div class="space-y-4">
                                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                                    <h5 class="font-semibold text-blue-900 mb-2">Windows Installation</h5>
                                    <ol class="list-decimal list-inside space-y-1 text-blue-800 text-sm">
                                        <li>Download the .exe installer from our download page</li>
                                        <li>Run the installer as administrator</li>
                                        <li>Follow the installation wizard</li>
                                        <li>Launch LeadWave from the desktop shortcut</li>
                                    </ol>
                                </div>
                                
                                <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                                    <h5 class="font-semibold text-green-900 mb-2">macOS Installation</h5>
                                    <ol class="list-decimal list-inside space-y-1 text-green-800 text-sm">
                                        <li>Download the .dmg file</li>
                                        <li>Open the .dmg and drag LeadWave to Applications</li>
                                        <li>Launch from Applications folder</li>
                                        <li>Allow permissions when prompted</li>
                                    </ol>
                                </div>
                            </div>
                        </div>
                        
                        <div id="first-setup" class="mb-12">
                            <h3 class="text-2xl font-semibold text-gray-900 mb-4">First Setup</h3>
                            <p class="text-gray-600 mb-4">
                                When you first launch LeadWave, you'll be guided through the initial setup process:
                            </p>
                            <ol class="list-decimal list-inside space-y-3 text-gray-600">
                                <li><strong>Create Account:</strong> Sign up for your LeadWave account or log in if you already have one</li>
                                <li><strong>Choose Plan:</strong> Select your subscription plan or start your free trial</li>
                                <li><strong>License Activation:</strong> Enter your license key to activate the software</li>
                                <li><strong>Initial Configuration:</strong> Set up your preferences and notification settings</li>
                            </ol>
                        </div>
                        
                        <div id="connecting-whatsapp" class="mb-12">
                            <h3 class="text-2xl font-semibold text-gray-900 mb-4">Connecting WhatsApp</h3>
                            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
                                <p class="text-yellow-800 text-sm">
                                    <strong>Important:</strong> Make sure WhatsApp Web is not open in any browser before connecting.
                                </p>
                            </div>
                            <ol class="list-decimal list-inside space-y-3 text-gray-600">
                                <li>Click "Add WhatsApp Account" in the main dashboard</li>
                                <li>A QR code will appear on screen</li>
                                <li>Open WhatsApp on your phone and go to Settings > Linked Devices</li>
                                <li>Tap "Link a Device" and scan the QR code</li>
                                <li>Your WhatsApp account is now connected and ready to use</li>
                            </ol>
                        </div>
                    </div>
                    
                    <!-- Features Section -->
                    <div id="features" class="mb-16">
                        <h2 class="text-3xl font-bold text-gray-900 mb-8">Features Guide</h2>
                        
                        <div id="bulk-messaging" class="mb-12">
                            <h3 class="text-2xl font-semibold text-gray-900 mb-4">Bulk Messaging</h3>
                            <p class="text-gray-600 mb-4">
                                Send personalized messages to multiple contacts simultaneously with our bulk messaging feature.
                            </p>
                            <div class="bg-gray-50 rounded-lg p-6">
                                <h4 class="font-semibold text-gray-900 mb-3">How to Send Bulk Messages</h4>
                                <ol class="list-decimal list-inside space-y-2 text-gray-600">
                                    <li>Navigate to "Bulk Messaging" in the main menu</li>
                                    <li>Select your target contacts or import a contact list</li>
                                    <li>Choose or create a message template</li>
                                    <li>Personalize with variables (name, company, etc.)</li>
                                    <li>Schedule or send immediately</li>
                                    <li>Monitor delivery status in real-time</li>
                                </ol>
                            </div>
                        </div>
                        
                        <div id="templates" class="mb-12">
                            <h3 class="text-2xl font-semibold text-gray-900 mb-4">Message Templates</h3>
                            <p class="text-gray-600 mb-4">
                                Create reusable message templates with dynamic variables for consistent communication.
                            </p>
                            <div class="bg-gray-50 rounded-lg p-6">
                                <h4 class="font-semibold text-gray-900 mb-3">Template Variables</h4>
                                <ul class="space-y-1 text-gray-600 text-sm">
                                    <li><code class="bg-gray-200 px-2 py-1 rounded">{{name}}</code> - Contact's name</li>
                                    <li><code class="bg-gray-200 px-2 py-1 rounded">{{company}}</code> - Company name</li>
                                    <li><code class="bg-gray-200 px-2 py-1 rounded">{{phone}}</code> - Phone number</li>
                                    <li><code class="bg-gray-200 px-2 py-1 rounded">{{custom_field}}</code> - Any custom field</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <!-- API Documentation -->
                    <div id="api-docs" class="mb-16">
                        <h2 class="text-3xl font-bold text-gray-900 mb-8">API Documentation</h2>
                        <p class="text-gray-600 mb-6">
                            Integrate LeadWave with your existing systems using our RESTful API.
                        </p>
                        
                        <div class="bg-gray-900 rounded-lg p-6 mb-6">
                            <h4 class="text-white font-semibold mb-3">Base URL</h4>
                            <code class="text-green-400">https://api.leadwave.com/v1/</code>
                        </div>
                        
                        <div class="space-y-6">
                            <div class="border border-gray-200 rounded-lg p-6">
                                <h4 class="font-semibold text-gray-900 mb-3">Send Message</h4>
                                <div class="bg-gray-50 rounded p-3 mb-3">
                                    <code class="text-sm">POST /messages/send</code>
                                </div>
                                <p class="text-gray-600 text-sm mb-3">Send a message to a specific contact</p>
                                <div class="bg-gray-900 rounded p-4 text-sm">
                                    <pre class="text-green-400">{
  "phone": "+1234567890",
  "message": "Hello {{name}}!",
  "variables": {
    "name": "John Doe"
  }
}</pre>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Troubleshooting -->
                    <div id="troubleshooting" class="mb-16">
                        <h2 class="text-3xl font-bold text-gray-900 mb-8">Troubleshooting</h2>
                        
                        <div class="space-y-6">
                            <div class="border border-gray-200 rounded-lg p-6">
                                <h4 class="font-semibold text-gray-900 mb-3">WhatsApp Connection Issues</h4>
                                <ul class="space-y-2 text-gray-600 text-sm">
                                    <li>• Ensure WhatsApp Web is not open in any browser</li>
                                    <li>• Check your internet connection</li>
                                    <li>• Try refreshing the QR code</li>
                                    <li>• Restart the application if needed</li>
                                </ul>
                            </div>
                            
                            <div class="border border-gray-200 rounded-lg p-6">
                                <h4 class="font-semibold text-gray-900 mb-3">Message Delivery Issues</h4>
                                <ul class="space-y-2 text-gray-600 text-sm">
                                    <li>• Verify contact phone numbers are correct</li>
                                    <li>• Check if contacts have blocked your number</li>
                                    <li>• Ensure you're not exceeding rate limits</li>
                                    <li>• Verify your account is in good standing</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Help Section -->
<section class="bg-gray-50 py-16">
    <div class="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
        <h2 class="text-3xl font-bold text-gray-900 mb-4">
            Need More Help?
        </h2>
        <p class="text-xl text-gray-600 mb-8">
            Can't find what you're looking for? Our support team is here to help.
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="{{ route('support') }}" class="btn-primary">Contact Support</a>
            <a href="#" class="btn-secondary">Join Community</a>
        </div>
    </div>
</section>
@endsection
