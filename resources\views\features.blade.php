@extends('layouts.app')

@section('title', 'Features - LeadWave WhatsApp Desktop')
@section('description', 'Discover powerful WhatsApp automation features including multi-device support, bulk messaging, AI chatbot, message templates, and more.')

@section('content')
<!-- Hero Section -->
<section class="bg-gradient-to-br from-primary-50 to-secondary-50 py-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <h1 class="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
                Powerful Features for
                <span class="text-primary-600">WhatsApp Automation</span>
            </h1>
            <p class="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
                Discover how LeadWave transforms your WhatsApp communication with advanced automation, 
                AI-powered responses, and comprehensive contact management.
            </p>
        </div>
    </div>
</section>

<!-- Features Grid -->
<section class="py-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Multi-Device Support -->
        <div class="mb-20">
            <div class="grid lg:grid-cols-2 gap-12 items-center">
                <div>
                    <div class="flex items-center mb-6">
                        <div class="w-12 h-12 gradient-bg rounded-lg flex items-center justify-center mr-4">
                            <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
                            </svg>
                        </div>
                        <h2 class="text-3xl font-bold text-gray-900">Multi-Device Support</h2>
                    </div>
                    <p class="text-lg text-gray-600 mb-6">
                        Connect and manage multiple WhatsApp accounts simultaneously from a single desktop application. 
                        Perfect for businesses managing different departments or multiple brands.
                    </p>
                    <ul class="space-y-3">
                        <li class="flex items-center">
                            <svg class="w-5 h-5 text-primary-600 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                            </svg>
                            <span class="text-gray-700">Connect up to 10 WhatsApp accounts</span>
                        </li>
                        <li class="flex items-center">
                            <svg class="w-5 h-5 text-primary-600 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                            </svg>
                            <span class="text-gray-700">Switch between accounts instantly</span>
                        </li>
                        <li class="flex items-center">
                            <svg class="w-5 h-5 text-primary-600 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                            </svg>
                            <span class="text-gray-700">Unified dashboard for all accounts</span>
                        </li>
                    </ul>
                </div>
                <div class="bg-gray-100 rounded-xl p-8">
                    <div class="bg-white rounded-lg p-4 shadow-lg">
                        <img src="{{ asset('images/features/multi-device.png') }}" alt="Multi-Device Support Dashboard" class="w-full h-auto rounded-lg">
                    </div>
                </div>
            </div>
        </div>

        <!-- Message Templates -->
        <div class="mb-20">
            <div class="grid lg:grid-cols-2 gap-12 items-center">
                <div class="order-2 lg:order-1">
                    <div class="bg-gray-100 rounded-xl p-8">
                        <div class="bg-white rounded-lg p-4 shadow-lg">
                            <img src="{{ asset('images/features/message-templates.png') }}" alt="Message Templates Library" class="w-full h-auto rounded-lg">
                        </div>
                    </div>
                </div>
                <div class="order-1 lg:order-2">
                    <div class="flex items-center mb-6">
                        <div class="w-12 h-12 gradient-bg rounded-lg flex items-center justify-center mr-4">
                            <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm0 2h12v8H4V6z"/>
                            </svg>
                        </div>
                        <h2 class="text-3xl font-bold text-gray-900">Message Templates</h2>
                    </div>
                    <p class="text-lg text-gray-600 mb-6">
                        Create, save, and reuse message templates for common responses. Personalize messages with 
                        dynamic variables and maintain consistent communication across your team.
                    </p>
                    <ul class="space-y-3">
                        <li class="flex items-center">
                            <svg class="w-5 h-5 text-primary-600 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                            </svg>
                            <span class="text-gray-700">Pre-built template library</span>
                        </li>
                        <li class="flex items-center">
                            <svg class="w-5 h-5 text-primary-600 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                            </svg>
                            <span class="text-gray-700">Dynamic variable insertion</span>
                        </li>
                        <li class="flex items-center">
                            <svg class="w-5 h-5 text-primary-600 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                            </svg>
                            <span class="text-gray-700">Team template sharing</span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Bulk Messaging -->
        <div class="mb-20">
            <div class="grid lg:grid-cols-2 gap-12 items-center">
                <div>
                    <div class="flex items-center mb-6">
                        <div class="w-12 h-12 gradient-bg rounded-lg flex items-center justify-center mr-4">
                            <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"/>
                                <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"/>
                            </svg>
                        </div>
                        <h2 class="text-3xl font-bold text-gray-900">Bulk Messaging</h2>
                    </div>
                    <p class="text-lg text-gray-600 mb-6">
                        Send personalized messages to thousands of contacts simultaneously. Schedule campaigns, 
                        track delivery rates, and manage your outreach efficiently.
                    </p>
                    <ul class="space-y-3">
                        <li class="flex items-center">
                            <svg class="w-5 h-5 text-primary-600 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                            </svg>
                            <span class="text-gray-700">Send to unlimited contacts</span>
                        </li>
                        <li class="flex items-center">
                            <svg class="w-5 h-5 text-primary-600 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                            </svg>
                            <span class="text-gray-700">Schedule message campaigns</span>
                        </li>
                        <li class="flex items-center">
                            <svg class="w-5 h-5 text-primary-600 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                            </svg>
                            <span class="text-gray-700">Delivery tracking & analytics</span>
                        </li>
                    </ul>
                </div>
                <div class="bg-gray-100 rounded-xl p-8">
                    <div class="bg-white rounded-lg p-4 shadow-lg">
                        <img src="{{ asset('images/features/bulk-messaging.png') }}" alt="Bulk Messaging Campaign Dashboard" class="w-full h-auto rounded-lg">
                    </div>
                </div>
            </div>
        </div>

        <!-- AI Chatbot -->
        <div class="mb-20">
            <div class="grid lg:grid-cols-2 gap-12 items-center">
                <div class="order-2 lg:order-1">
                    <div class="bg-gray-100 rounded-xl p-8">
                        <div class="bg-white rounded-lg p-4 shadow-lg">
                            <img src="{{ asset('images/features/ai-chatbot.png') }}" alt="AI Chatbot Interface" class="w-full h-auto rounded-lg">
                        </div>
                    </div>
                </div>
                <div class="order-1 lg:order-2">
                    <div class="flex items-center mb-6">
                        <div class="w-12 h-12 gradient-bg rounded-lg flex items-center justify-center mr-4">
                            <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                        </div>
                        <h2 class="text-3xl font-bold text-gray-900">AI-Powered Chatbot</h2>
                    </div>
                    <p class="text-lg text-gray-600 mb-6">
                        Deploy intelligent chatbots that understand context and provide human-like responses.
                        Handle customer inquiries 24/7 with advanced natural language processing.
                    </p>
                    <ul class="space-y-3">
                        <li class="flex items-center">
                            <svg class="w-5 h-5 text-primary-600 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                            </svg>
                            <span class="text-gray-700">Natural language understanding</span>
                        </li>
                        <li class="flex items-center">
                            <svg class="w-5 h-5 text-primary-600 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                            </svg>
                            <span class="text-gray-700">24/7 automated responses</span>
                        </li>
                        <li class="flex items-center">
                            <svg class="w-5 h-5 text-primary-600 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                            </svg>
                            <span class="text-gray-700">Seamless human handoff</span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Contact Management -->
        <div class="mb-20">
            <div class="grid lg:grid-cols-2 gap-12 items-center">
                <div>
                    <div class="flex items-center mb-6">
                        <div class="w-12 h-12 gradient-bg rounded-lg flex items-center justify-center mr-4">
                            <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"/>
                            </svg>
                        </div>
                        <h2 class="text-3xl font-bold text-gray-900">Contact Management</h2>
                    </div>
                    <p class="text-lg text-gray-600 mb-6">
                        Organize and manage your contacts with advanced segmentation, tagging, and filtering.
                        Import from CSV, sync with CRM systems, and maintain detailed contact profiles.
                    </p>
                    <ul class="space-y-3">
                        <li class="flex items-center">
                            <svg class="w-5 h-5 text-primary-600 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                            </svg>
                            <span class="text-gray-700">Advanced contact segmentation</span>
                        </li>
                        <li class="flex items-center">
                            <svg class="w-5 h-5 text-primary-600 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                            </svg>
                            <span class="text-gray-700">CSV import & CRM sync</span>
                        </li>
                        <li class="flex items-center">
                            <svg class="w-5 h-5 text-primary-600 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                            </svg>
                            <span class="text-gray-700">Custom tags & labels</span>
                        </li>
                    </ul>
                </div>
                <div class="bg-gray-100 rounded-xl p-8">
                    <div class="bg-white rounded-lg p-4 shadow-lg">
                        <img src="{{ asset('images/features/contact-management.png') }}" alt="Contact Management System" class="w-full h-auto rounded-lg">
                    </div>
                </div>
            </div>
        </div>

        <!-- Call Automation -->
        <div class="mb-20">
            <div class="grid lg:grid-cols-2 gap-12 items-center">
                <div class="order-2 lg:order-1">
                    <div class="bg-gray-100 rounded-xl p-8">
                        <div class="bg-white rounded-lg p-4 shadow-lg">
                            <img src="{{ asset('images/features/call-automation.png') }}" alt="Call Automation Dashboard" class="w-full h-auto rounded-lg">
                        </div>
                    </div>
                </div>
                <div class="order-1 lg:order-2">
                    <div class="flex items-center mb-6">
                        <div class="w-12 h-12 gradient-bg rounded-lg flex items-center justify-center mr-4">
                            <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"/>
                            </svg>
                        </div>
                        <h2 class="text-3xl font-bold text-gray-900">Call Automation</h2>
                    </div>
                    <p class="text-lg text-gray-600 mb-6">
                        Automate voice calls with intelligent call routing, voicemail detection, and follow-up
                        sequences. Integrate seamlessly with your WhatsApp campaigns.
                    </p>
                    <ul class="space-y-3">
                        <li class="flex items-center">
                            <svg class="w-5 h-5 text-primary-600 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                            </svg>
                            <span class="text-gray-700">Automated call sequences</span>
                        </li>
                        <li class="flex items-center">
                            <svg class="w-5 h-5 text-primary-600 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                            </svg>
                            <span class="text-gray-700">Voicemail detection</span>
                        </li>
                        <li class="flex items-center">
                            <svg class="w-5 h-5 text-primary-600 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                            </svg>
                            <span class="text-gray-700">Call analytics & reporting</span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="bg-primary-600 py-16">
    <div class="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
        <h2 class="text-3xl font-bold text-white mb-4">
            Ready to Transform Your WhatsApp Communication?
        </h2>
        <p class="text-xl text-primary-100 mb-8">
            Start your free trial today and experience the power of automated WhatsApp marketing.
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="{{ route('download') }}" class="bg-white text-primary-600 font-semibold py-3 px-8 rounded-lg hover:bg-gray-100 transition-colors">
                Download Free Trial
            </a>
            <a href="{{ route('pricing') }}" class="border-2 border-white text-white font-semibold py-3 px-8 rounded-lg hover:bg-white hover:text-primary-600 transition-colors">
                View Pricing
            </a>
        </div>
    </div>
</section>
@endsection
