<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

Route::get('/', function () {
    return view('home');
})->name('home');

Route::get('/features', function () {
    return view('features');
})->name('features');

Route::get('/pricing', function () {
    return view('pricing');
})->name('pricing');

Route::get('/download', function () {
    return view('download');
})->name('download');

Route::get('/reseller', function () {
    return view('reseller');
})->name('reseller');

Route::get('/documentation', function () {
    return view('documentation');
})->name('documentation');

Route::get('/support', function () {
    return view('support');
})->name('support');

Route::get('/contact', function () {
    return view('contact');
})->name('contact');
