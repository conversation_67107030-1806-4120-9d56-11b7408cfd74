<?php $__env->startSection('title', 'Download - LeadWave WhatsApp Desktop'); ?>
<?php $__env->startSection('description', 'Download LeadWave WhatsApp Desktop for Windows, Mac, and Linux. Start your free trial today and automate your WhatsApp communication.'); ?>

<?php $__env->startSection('content'); ?>
<!-- Hero Section -->
<section class="bg-gradient-to-br from-primary-50 to-secondary-50 py-20">
    <div class="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
        <h1 class="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
            Download
            <span class="text-primary-600">LeadWave</span>
        </h1>
        <p class="text-xl text-gray-600 mb-8">
            Get started with your 14-day free trial. No credit card required.
        </p>
        <div class="bg-white rounded-lg p-6 shadow-lg inline-block">
            <div class="text-sm text-gray-500 mb-2">Latest Version</div>
            <div class="text-2xl font-bold text-gray-900">v2.1.4</div>
            <div class="text-sm text-gray-500">Released: June 28, 2025</div>
        </div>
    </div>
</section>

<!-- Download Options -->
<section class="py-20">
    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid md:grid-cols-3 gap-8 mb-16">
            <!-- Windows -->
            <div class="card p-8 text-center">
                <div class="w-16 h-16 mx-auto mb-6 bg-blue-100 rounded-lg flex items-center justify-center">
                    <svg class="w-8 h-8 text-blue-600" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M0 3.449L9.75 2.1v9.451H0m10.949-9.602L24 0v11.4H10.949M0 12.6h9.75v9.451L0 20.699M10.949 12.6H24V24l-13.051-1.351"/>
                    </svg>
                </div>
                <h3 class="text-xl font-bold text-gray-900 mb-4">Windows</h3>
                <p class="text-gray-600 mb-6">Compatible with Windows 10 and 11</p>
                <div class="space-y-3">
                    <a href="#" class="block w-full btn-primary">
                        Download for Windows
                        <span class="block text-sm opacity-75">(64-bit, 89.2 MB)</span>
                    </a>
                    <a href="#" class="block w-full text-primary-600 hover:text-primary-700 text-sm">
                        Download 32-bit version
                    </a>
                </div>
            </div>

            <!-- macOS -->
            <div class="card p-8 text-center">
                <div class="w-16 h-16 mx-auto mb-6 bg-gray-100 rounded-lg flex items-center justify-center">
                    <svg class="w-8 h-8 text-gray-600" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z"/>
                    </svg>
                </div>
                <h3 class="text-xl font-bold text-gray-900 mb-4">macOS</h3>
                <p class="text-gray-600 mb-6">Compatible with macOS 10.15+</p>
                <div class="space-y-3">
                    <a href="#" class="block w-full btn-primary">
                        Download for Mac
                        <span class="block text-sm opacity-75">(Intel & Apple Silicon, 92.1 MB)</span>
                    </a>
                    <a href="#" class="block w-full text-primary-600 hover:text-primary-700 text-sm">
                        Download from App Store
                    </a>
                </div>
            </div>

            <!-- Linux -->
            <div class="card p-8 text-center">
                <div class="w-16 h-16 mx-auto mb-6 bg-orange-100 rounded-lg flex items-center justify-center">
                    <svg class="w-8 h-8 text-orange-600" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12.504 0c-.155 0-.315.008-.48.021-4.226.333-3.105 4.807-3.17 6.298-.076 1.092-.3 1.953-1.05 3.02-.885 1.051-2.127 2.75-2.716 4.521-.278.832-.41 1.684-.287 2.489a.424.424 0 00-.11.135c-.26.268-.45.6-.663.839-.199.199-.485.267-.797.4-.313.136-.658.269-.864.68-.09.189-.136.394-.132.602 0 .199.027.4.055.536.058.399.116.728.04.97-.249.68-.28 1.145-.106 1.484.174.334.535.47.94.601.81.2 1.91.135 2.774.6.926.466 1.866.67 2.616.47.526-.116.97-.464 1.208-.946.587-.003 1.23-.269 2.26-.334.699-.058 1.574.267 2.577.2.025.134.063.198.114.333l.003.003c.391.778 1.113 1.132 1.884 1.071.771-.06 1.592-.536 2.257-1.306.631-.765 1.683-1.084 2.378-1.503.348-.199.629-.469.649-.853.023-.4-.2-.811-.714-1.376v-.097l-.003-.003c-.17-.2-.25-.535-.338-.926-.085-.401-.182-.786-.492-1.046h-.003c-.059-.054-.123-.067-.188-.135a.357.357 0 00-.19-.064c.431-1.278.264-2.55-.173-3.694-.533-1.41-1.465-2.638-2.175-3.483-.796-1.005-1.576-1.957-1.56-3.368.026-2.152.236-6.133-3.544-6.139zm.529 3.405h.013c.213 0 .396.062.584.198.19.135.33.332.438.533.105.259.158.459.166.724 0-.02.006-.04.006-.06v.105a.086.086 0 01-.004-.021l-.004-.024a1.807 1.807 0 01-.15.706.953.953 0 01-.213.335.71.71 0 01-.088.066c-.297.168-.623.336-.985.328-.27-.005-.52-.165-.705-.405-.131-.167-.212-.365-.241-.572-.013-.108-.02-.216-.02-.323 0-.108.007-.216.02-.323.015-.66.238-1.104.681-1.2.145-.033.29-.049.435-.048l.067-.001zm2.674 4.756c.331 0 .565.106.745.196.159.073.606.365.606.365v.006c0 .057-.101.129-.48.129-.32 0-.74-.129-.740-.129s-.296-.058-.432-.058c-.19 0-.33.058-.33.058s-.4.129-.74.129c-.38 0-.48-.072-.48-.129v-.006s.447-.292.606-.365c.18-.09.414-.196.745-.196zm-1.324 1.27a1.49 1.49 0 011.324 0 1.49 1.49 0 011.324 0c.04.016.055.024.055.024a.834.834 0 01.606.365v.006c0 .057-.101.129-.48.129-.32 0-.74-.129-.74-.129s-.296-.058-.432-.058c-.19 0-.33.058-.33.058s-.4.129-.74.129c-.38 0-.48-.072-.48-.129v-.006a.834.834 0 01.606-.365s.015-.008.055-.024z"/>
                    </svg>
                </div>
                <h3 class="text-xl font-bold text-gray-900 mb-4">Linux</h3>
                <p class="text-gray-600 mb-6">Ubuntu, Debian, CentOS, Fedora</p>
                <div class="space-y-3">
                    <a href="#" class="block w-full btn-primary">
                        Download .deb
                        <span class="block text-sm opacity-75">(64-bit, 87.8 MB)</span>
                    </a>
                    <a href="#" class="block w-full text-primary-600 hover:text-primary-700 text-sm">
                        Download .rpm
                    </a>
                </div>
            </div>
        </div>

        <!-- System Requirements -->
        <div class="bg-gray-50 rounded-xl p-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-6 text-center">System Requirements</h2>
            <div class="grid md:grid-cols-3 gap-8">
                <div>
                    <h3 class="font-semibold text-gray-900 mb-3">Windows</h3>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>• Windows 10 or later</li>
                        <li>• 4GB RAM minimum</li>
                        <li>• 500MB free disk space</li>
                        <li>• Internet connection</li>
                    </ul>
                </div>
                <div>
                    <h3 class="font-semibold text-gray-900 mb-3">macOS</h3>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>• macOS 10.15 or later</li>
                        <li>• 4GB RAM minimum</li>
                        <li>• 500MB free disk space</li>
                        <li>• Internet connection</li>
                    </ul>
                </div>
                <div>
                    <h3 class="font-semibold text-gray-900 mb-3">Linux</h3>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>• Ubuntu 18.04+ / Debian 10+</li>
                        <li>• 4GB RAM minimum</li>
                        <li>• 500MB free disk space</li>
                        <li>• Internet connection</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Installation Guide -->
<section class="bg-white py-20">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">Quick Installation Guide</h2>
            <p class="text-lg text-gray-600">Get up and running in minutes</p>
        </div>

        <div class="grid md:grid-cols-3 gap-8">
            <div class="text-center">
                <div class="w-12 h-12 bg-primary-600 text-white rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold">1</div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Download</h3>
                <p class="text-gray-600">Download the installer for your operating system</p>
            </div>
            <div class="text-center">
                <div class="w-12 h-12 bg-primary-600 text-white rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold">2</div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Install</h3>
                <p class="text-gray-600">Run the installer and follow the setup wizard</p>
            </div>
            <div class="text-center">
                <div class="w-12 h-12 bg-primary-600 text-white rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold">3</div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Connect</h3>
                <p class="text-gray-600">Connect your WhatsApp account and start automating</p>
            </div>
        </div>
    </div>
</section>

<!-- Trial Information -->
<section class="bg-primary-600 py-16">
    <div class="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
        <h2 class="text-3xl font-bold text-white mb-4">
            14-Day Free Trial
        </h2>
        <p class="text-xl text-primary-100 mb-8">
            No credit card required. Full access to all features. Cancel anytime.
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="<?php echo e(route('pricing')); ?>" class="bg-white text-primary-600 font-semibold py-3 px-8 rounded-lg hover:bg-gray-100 transition-colors">
                View Pricing Plans
            </a>
            <a href="<?php echo e(route('support')); ?>" class="border-2 border-white text-white font-semibold py-3 px-8 rounded-lg hover:bg-white hover:text-primary-600 transition-colors">
                Need Help?
            </a>
        </div>
    </div>
</section>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\My Projects\LeadLand\resources\views/download.blade.php ENDPATH**/ ?>